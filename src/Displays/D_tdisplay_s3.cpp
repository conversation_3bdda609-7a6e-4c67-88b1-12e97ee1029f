#include <Arduino.h>
#include "D_tdisplay_s3.h"
#include "DataSources/DataSource.h"
// #define TINY u8g2_font_profont10_tf
#define SMALL &FreeMonoBold9pt7b
#define MEDIUM &FreeSansBold12pt7b
#define MEDLAR &FreeSansBold18pt7b
#define LARGE &FreeSansBold24pt7b
#define gray 0x6B6D
#define blue 0x0AAD
#define orange 0xC260
#define purple 0x604D
#define green 0x1AE9

D_tdisplay_s3::D_tdisplay_s3(Config *config):Display(config)  // call Base class constructor with config object
	
{
	spl("in tdisplay_s3 Constructor");

	m_lcd = new TFT_eSPI(170, 320);
	m_sprite = new TFT_eSprite(m_lcd);

	spl("out of Constructor");
}
void D_tdisplay_s3::setup() {
	spl("in tdisplay setup");

	pinMode(PIN_LCD_POWER, OUTPUT);
	pinMode(TFT_BL, OUTPUT);    // BackLight enable pin

	digitalWrite(PIN_LCD_POWER, HIGH);
	digitalWrite(TFT_BL, HIGH);

	m_config->m_display_hw = "tft";
	m_lcd->init();
	m_lcd->setRotation(3);
	m_lcd->setSwapBytes(true);

	m_progress_indicator = (m_config->gets("d_opts").indexOf('p') > -1);
	m_upsidedown = (m_config->gets("upsidedown").indexOf("rue") > -1);
	if (m_upsidedown) {
		spl2("upside down set ",m_config->gets("upsidedown"));
		m_lcd->setRotation(1);
	}
	
}

void D_tdisplay_s3::loop() {
	//spl("in D_tdisplay_s3 loop");
	if (m_config->night_mode_active(m_data->geti("hour")) && !m_config->night_mode_continues ) {
		if (m_config->night_mode_mode().equals("dim")) {
			set_brightness("Low");
		} else {
			// default is to just turn off the display
			special("off","blah");
			return;
		}
	}
	bool force_display = false;
	// check to see if the backlight was turned off and turn it back on and reset member variable
	if (m_display_active == false) {
		digitalWrite(TFT_BL, HIGH);
		m_display_active = true;
		force_display = true;
	}

	if (m_data->m_new_data || force_display) {
		spl("Got new data or forced");
		show_data();
		m_data->m_new_data = false;
		m_last_activity = millis();
		m_last_update_time = millis();

	}
	if (m_progress_indicator) {
		draw_update_indicator();
	}
	if (m_animate) {
		show_data("scroll");
	}
	// if (m_idle_timeout > 0) {
	// 	if (millis() > m_last_activity + m_idle_timeout) {
	// 		spl("idle timeout");
	// 		special("clear","blah");
	// 		m_last_activity = millis();

	// 	}
	// }


}

void D_tdisplay_s3::startup_message(String startup) {
	if (startup.isEmpty()) {
		if (m_config->gets("dtype").indexOf("wind") > -1) {
			logo("Windy-Tron","Color TFT");
		} else {
			logo("GeneralTron","Color TFT");
		}
	} else {
			logo("Windy-Tron",startup);
	}
}


void D_tdisplay_s3::wifi_connected() {
	startup_message(m_config->gets("startup_message"));
	m_lcd->setFreeFont(SMALL);
	m_lcd->setCursor(40,13);
	m_lcd->print("Connected!");
}

// do special stuff pertaining to display type and such
void D_tdisplay_s3::special(String m, String s) {
	if (m.equals("clear") || m.equals("off")) {
		m_lcd->fillScreen(TFT_BLACK);
		pinMode(TFT_BL, OUTPUT);
		digitalWrite(TFT_BL, LOW);
		m_display_active = false;
	} else if (m.equals("fetching")) {
		m_lcd->fillScreen(TFT_BLACK);
		display_location("Fetching . . . ");
	} else if (m.equals("wifi_connect")) {
		m_sprite->setFreeFont(MEDIUM);
		m_sprite->drawString("WiFi Connecting",220,20,4);
		// m_lcd->setFreeFont(SMALL);
		// m_lcd->setCursor(40,13);
		// m_lcd->print("Connecting to WiFi");
	} else if (m.equals("wifi_config")) {
		m_lcd->pushImage(0, 0, 320, 170, tft_bitmap_windy_tron_320_color_gradient);

		m_lcd->setFreeFont(MEDIUM);
		m_lcd->setCursor(10,20);
		m_lcd->print("Connect to Tron_Config, \npassword windytron");
	}
}
void D_tdisplay_s3::show_data() {
	m_last_i = 0;

	fade_out();
	m_lcd->fillScreen(TFT_BLACK);
	spl("in tds3 show_data");
	// check for night_mode before doing any displaying
    bool should_return = true;
    if (m_config->night_mode_active(m_data->geti("hour"))) {
      spl("nightmode active");
      if (m_config->night_mode_mode().equals("off")) {
        spl("night_mode:off");
        special("clear","blah"); // turn off the display
        //m_data.cdelay(update_delay_millis);
      } else if (m_config->night_mode_mode().equals("dim")) {
        set_brightness("Low");      
        should_return = false; // don't return because we want to do normal display but at low brightness
      }
      // else if (m_config->night_mode_mode().equals("rain")) {
      //   //spl("night_mode:rain");
      //   //rain(update_delay_millis);
      // return;
      // }
      // else if (m_config->night_mode_mode().equals("wind")) {
      //   //wind(update_delay_millis,m_data->get("avg);
      //   //return;
      // }
      // else if (m_config->night_mode_mode().equals("matrix")) {
      //   //rain(update_delay_millis,true);  // true upsidedown turns rain into matrix ;}
      //   //return;
      // }
      else if (m_config->night_mode_mode().equals("graph")) {
        if (m_config->night_mode_continues) return;   // don't re-display is it's already been night mode
        //display_history();
        
      }
      m_config->night_mode_continues = true;
	  m_data->m_new_data = false;  // it may be new but we aint gonna display it.
      if (should_return) return;
    }
    else {
    //   spl("night mode inactive");
		set_brightness(m_config->gets("brightness"),m_data->geti("hour"));
    }

	if (m_config->display_option("a") > -1) {   // a for anti-burn
		Serial.println("antiburn");
    	anti_burn();
  	}

	String dtype = m_config->gets("dtype");
	if (dtype.equals("text") || dtype.isEmpty()) {
		String data = m_data->get("data");
		//string(data,1,true);
		string_scale(data,1,true);

	} else if (m_data->get("type").indexOf("rows") > -1) {
		spl2("page type:",m_data->get("type"));
		display_time(m_data->geti("minute"), m_data->geti("hour"));

		display_label(m_data->get("label"));
		int num_rows = m_data->get("num_rows").toInt();
		// print out all the row data
		spl2("num rows:",num_rows);
		for (int row = 1; row <= num_rows ; row++) {
			if (!m_data->get("row"+String(row)).isEmpty()){
				spl2("row"+String(row),m_data->get("row"+String(row)));
				display_row(m_data->get("row"+String(row)),row); //pass the data and the row number
			}
		}
	} else if (m_data->get("type").indexOf("tide") > -1) {
		spl2("page type:",m_data->get("type"));
		//display_time(m_data->geti("minute"), m_data->geti("hour"));
		display_date();
		display_label(m_data->get("label"));
		display_tides(); // will pull tide data from m_data directly
		//m_lcd->update();

	
	// old school wind data via simple text string.  datasource base class knows how to parse all the different data type.   
	} else if (m_data->get("type").indexOf("wind") > -1 || dtype.indexOf("wind") > -1) {
		// this is the big one gotta display all the stuff
		if (m_data->get("label").isEmpty() && m_config->gets("label").isEmpty()) {
			display_location(m_config->gets("startup_message"));
		} else {
			if (m_data->get("label").isEmpty()) {
				display_location(m_config->gets("label"));
			} else {
				display_location(m_data->get("label"));
			} 
		}
        display_time(m_data->geti("minute"), m_data->geti("hour"));
		display_vel(m_data->get("avg"),m_data->get("gust"));
		display_dir(m_data->get("dir_card"),m_data->get("dir_deg"));
		display_swell(m_data->get("aux1"));  
		display_swell2(m_data->get("aux2"));

	}
	// fade in
	// for (int i = 0; i <= m_current_brightness; i++) {
	// 	analogWrite(TFT_BL, i);
	// 	delay(5); // Adjust delay to control the wipe speed
	// }
	m_odd_update = !m_odd_update;
}

void D_tdisplay_s3::show_data(String effect) {
	spl("in oled data(effect)");
	String dtype= m_config->gets("dtype");
	if (dtype.equals("text") || dtype.isEmpty()) {
		String data = m_data->get("data");
		string(data,1,true);
		if (data.length() > 12) m_animate = true;

	} else if (dtype.indexOf("wind") > -1 ) {
		// this is the big one gotta display all the stuff

	}
	//String new_data = m_data->m_api_data;
	//string(new_data,1,true);
}

// display_row function
void D_tdisplay_s3::display_row(String s, int row, int color) {
	//spl("in display_row");
	int start = 40;
	m_lcd->setTextColor(color);
	m_lcd->setFreeFont(MEDIUM);
	m_lcd->setCursor(5,start+row*35);
	m_lcd->print(s);

}


void D_tdisplay_s3::display_tides() {
		//0129 H 1.5 ft ~ 0749 N 0.5 ft ~ 1500 H 2.5 ft ~ 2114 L 0.6 ft ~
	int num_tides = m_data->geti("num_tides");
	if  (num_tides < 1) return;
		int start = 70;
		int spacing = 30;
	if (num_tides < 4) {
		start = 80;
		spacing = 40;
	}    
	// m_lcd->setFreeFont(&FreeMonoBold12pt7b);
	m_lcd->setFreeFont(&FreeSansBold12pt7b);
	//m_lcd->setTextColor(TFT_WHITE);
	for (int i = 0 ; i < num_tides ; i++) {
		String tide_str = m_data->get("tide"+String(i));
		if (tide_str.indexOf('L') > 0) {
			if (tide_str.indexOf('-')) {
				// we have a negative tide color differently
				m_lcd->setTextColor(TFT_RED);
			} else {
				m_lcd->setTextColor(TFT_ORANGE);
			}
		} else {
			m_lcd->setTextColor(TFT_GREENYELLOW);
		}
		//m_lcd->setTextColor(color);
		m_lcd->setFreeFont(MEDIUM);
		m_lcd->setCursor(75,start+i*spacing);
		m_lcd->print(tide_str);
  }
}


void D_tdisplay_s3::string_scale(String s, int holdtime, bool persist) {
	unsigned int length = s.length();
	if (length == 0) { 
		//m_u8g2->clear();
		return;
	}
	//m_u8g2->clear();
	spl("in D_tdisplay_s3 string_scale");
	if (length < 8) {
			//m_u8g2->setFont(LARGE);		
	} else if (length < 14) {
			//m_u8g2->setFont(MEDIUM);
	} else {
			//m_u8g2->setFont(SMALL);
	}
	char ca[50];
	s.toCharArray(ca,50);
	m_xpos = 0; m_ypos = 0;
	//m_xpos = ((m_u8g2->getDisplayWidth() - (m_u8g2->getUTF8Width(ca))) / 2);
	
	if (length > 10 && false) {  // worry about scrolling later
		// // scroll
		// for (uint i = 0; i < 63 ; i++ ) {
		// 	//m_u8g2->clear();	
		// 	//m_u8g2->drawStr(128-i*4,0, ca);
		// 	//m_u8g2->sendBuffer();
		// 	//m_u8g2->getStrWidth(s.c_str());
		// }
	} else {
		//m_u8g2->drawStr(m_xpos,20, ca);
		//m_u8g2->sendBuffer();
	}
	delay(holdtime);
	if (!persist) {
		//m_u8g2->clear();
		//m_u8g2->sendBuffer();
	}	
}


void D_tdisplay_s3::string (String s, int holdtime, bool persist) {

	if (s.length() < 6) {
		string_scale(s,1000,true);
		return;
	}
	//m_u8g2->clear();
	spl("in D_tdisplay_s3 display string");
	//m_u8g2->setFont(SMALL);
	char ca[50];
	s.toCharArray(ca,50);
	m_xpos = 0; m_ypos = 0;
	unsigned int length = s.length();
	if (length > 10 && false) {  // worry about scrolling later
		// // scroll
		// for (uint i = 0; i < 63 ; i++ ) {
		// 	//m_u8g2->clear();	
		// 	//m_u8g2->drawStr(128-i*4,0, ca);
		// 	//m_u8g2->sendBuffer();
		// 	//m_u8g2->getStrWidth(s.c_str());
		// }
	} else {

		m_lcd->setFreeFont(SMALL);
		m_lcd->setCursor(0,10);
		m_lcd->print(s);
		//m_u8g2->drawStr(0,0, ca);
		//m_u8g2->sendBuffer();
	}
	delay(holdtime);
	if (!persist) {
		//m_u8g2->clear();
		//m_u8g2->sendBuffer();
	}

}

void D_tdisplay_s3::fade_out() {
	for (int i = m_current_brightness; i > 0; i--) {
		analogWrite(TFT_BL, i);
		delay(5); // Adjust delay to control the wipe speed
	}
}
void D_tdisplay_s3::fade_in() {
	for (int i = 0; i <= m_current_brightness; i++) {
		analogWrite(TFT_BL, i);
		delay(5); // Adjust delay to control the wipe speed
	}
}
void D_tdisplay_s3::anti_burn() {

	digitalWrite(TFT_BL,LOW);
	m_lcd->pushImage(0, 0, 320, 170, tft_bitmap_windy_tron_320_color_gradient);
	int x = 140;
	int y = 60;
	m_lcd->setFreeFont(MEDIUM);
	m_lcd->setCursor(x, y);
	m_lcd->print("Updating . . ");	// fade in the screen

	fade_in();
	fade_out();
	m_lcd->fillScreen(TFT_BLACK);

}

void D_tdisplay_s3::logo(String big, String small) {
	set_brightness(m_config->gets("brightness"));
	if (big.equals("Windy-Tron")) {
		m_lcd->pushImage(0, 0, 320, 170, tft_bitmap_windy_tron_320_color_gradient);
		int x = 140;
		int y = 60;
		if (small.length() > 13) {
			x = 20;
			y = 30;
		}
		m_lcd->setFreeFont(MEDIUM);
		m_lcd->setCursor(x,y);
		m_lcd->print(small);

		// Display MAC address for first 5 boots
		if (m_config->geti("boot_num") <= 5) {
			m_lcd->setFreeFont(SMALL);  // Using smaller font for MAC
			m_lcd->setCursor(25, 160);  // Adjust position as needed
			m_lcd->print(WiFi.macAddress());
		}
	} else {
		m_lcd->setFreeFont(LARGE);
		m_lcd->setCursor(25,50);
		m_lcd->print(big);

		m_lcd->setFreeFont(MEDIUM);
		m_lcd->setCursor(75,100);
		m_lcd->print(small);

		// Display MAC address for first 5 boots
		if (m_config->geti("boot_num") <= 5) {
			m_lcd->setFreeFont(SMALL);  // Using smaller font for MAC
			m_lcd->setCursor(25, 160);  // Adjust position as needed
			m_lcd->print(WiFi.macAddress());
		}
	}
}

void D_tdisplay_s3::display_time(int minute, int hour) {
	m_lcd->setTextColor(TFT_WHITE);
  m_lcd->setFreeFont(MEDIUM);
  m_lcd->setCursor(250, 19);
  char m[3];
  sprintf(m,"%02i", minute);
  m_lcd->print(String(hour)+":"+String(m));
}

void D_tdisplay_s3::display_date() {
	m_lcd->setTextColor(TFT_WHITE);
	m_lcd->setFreeFont(MEDIUM);
	m_lcd->setCursor(250, 19);
	char m[3];
	String month = m_data->get("month");
	String day = m_data->get("day");
	m_lcd->print(month+"/"+day);  

}

void D_tdisplay_s3::display_location(String s) {
	m_lcd->setTextColor(TFT_WHITE);
	int x = 100;
	if (s.length() > 12 ) x = 10; // move the x coord over if long name 
	m_lcd->setCursor(x,19);
	m_lcd->setFreeFont(MEDIUM);
	m_lcd->setTextSize(1);
	m_lcd->print(s);
	
}

void D_tdisplay_s3::display_label(String s) {
	m_lcd->setTextColor(TFT_WHITE);
	int x = 20;
	if (s.length() > 10 ) x = 5; // move the x coord over if long name 
	m_lcd->setCursor(x,30);
	m_lcd->setFreeFont(MEDLAR);
	m_lcd->setTextSize(1);
	m_lcd->print(s);
	
}

void D_tdisplay_s3::display_vel(String avg, String gust) {
	if (avg.toInt() < 15) m_lcd->setTextColor(TFT_BLUE);
	if (avg.toInt() >= 15) m_lcd->setTextColor(TFT_CYAN);
	if (avg.toInt() >= 25) m_lcd->setTextColor(TFT_GREEN);
	if (avg.toInt() >= 30) m_lcd->setTextColor(TFT_MAGENTA);
	if (avg.toInt() >= 35) m_lcd->setTextColor(TFT_RED);
	int y = 90;
	if (m_data->get("aux1").length() <= 1) y += 25;
	m_lcd->setCursor(120,y);
	m_lcd->setFreeFont(MEDLAR);
	m_lcd->setTextSize(2);
	String s = String(avg + "g" + gust);
	m_lcd->print(avg);
	m_lcd->setFreeFont(&FreeSans12pt7b);
	m_lcd->print('g');
	m_lcd->setFreeFont(MEDLAR);
	m_lcd->print(gust);
}
// NE 44 20g27 , 1.0f,11s,E_3.6f,4s,E - 2024-08-01T14:04:12+2333333443434
void D_tdisplay_s3::display_dir(String card,String deg) {
	int y = 58;
	if (m_data->get("aux1").length() <= 1) y += 25;
	if (m_config->gets("api_data_url").indexOf("maui") > -1 || m_config->gets("color_profile").equals("Maui") ) {
		m_lcd->setTextColor(TFT_RED);
		if (card == "N") m_lcd->setTextColor(TFT_CYAN);
		if (card == "NE") m_lcd->setTextColor(TFT_GREEN);
		if (card == "ENE") m_lcd->setTextColor(TFT_YELLOW);
	} else {
		m_lcd->setTextColor(TFT_CYAN);
	}

	m_lcd->setFreeFont(MEDLAR);
	m_lcd->setTextSize(1);
	m_lcd->setCursor(5,y);
	m_lcd->print(card);
	m_lcd->setFreeFont(MEDLAR);
	m_lcd->setCursor(5,y+32);
	m_lcd->print(deg+"*");  // for now we'll use * for degree symbol
}

void D_tdisplay_s3::display_swell(String s) {
	m_lcd->setTextColor(TFT_WHITE);
  	m_lcd->setFreeFont(SMALL);
	m_lcd->setTextSize(1);
	m_lcd->setCursor(10,135);
	m_lcd->print(s);
}
void D_tdisplay_s3::display_swell2(String s) {
	m_lcd->setTextColor(TFT_CYAN);
  	m_lcd->setFreeFont(SMALL);
	m_lcd->setTextSize(1);
	m_lcd->setCursor(10,160);
	m_lcd->print(s);

}

void D_tdisplay_s3::draw_update_indicator() {
  int i = (millis() - m_last_update_time) / m_progress_dot_time;  // should give us which pixel to draw at
    if (i < 0 and i > 320) return;   // sanity, can't turn on leds out of bounds.
    if (i != m_last_i) {
		m_lcd->drawCircle(320-i,170,2,TFT_MAROON);
		m_last_i = i;
    }

}

void D_tdisplay_s3::wifi_sleep() {

}

void D_tdisplay_s3::set_brightness(int raw) {
	analogWrite(TFT_BL, raw);
	m_current_brightness = raw;
}
void D_tdisplay_s3::set_brightness(String brightness,int hour) {
	spl("in tds3 set_brightness");
	if (String("Auto").equals(brightness)) {
		if (hour > 17 || hour < 8) {
			brightness = "Low";
		}
	}
	byte bright_byte;
	if (String("Med").equals(brightness)) {
		bright_byte = 150;
	} else if (String("Low").equals(brightness)) {
		bright_byte = 50;
	} else { //if (String("High").equals(brightness)) {
		bright_byte = 255;
	}
	spl2("brightness: ",bright_byte);
	analogWrite(TFT_BL, bright_byte );
}
