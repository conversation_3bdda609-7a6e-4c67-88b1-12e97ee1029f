#include <Config.h>
#include "api_secrets.h"

Config::Config(String mac) {
	mac.replace(":", "");
	String l_device_name = "Tron-" + mac;
	spl(l_device_name);

	m_mac_address = mac;
	m_device_name = l_device_name;

	m_config_map = new SimpleMap<String, String>([](String& a, String& b) -> int {
		if (a == b) return 0;      // a and b are equal
		else if (a > b) return 1;  // a is bigger than b
		else return -1;            // a is smaller than b
		});
}

void Config::setup(bool format = false) {
	if (format) {
		Serial.println("formatting....");
		LittleFS.format();
	}
	bool config_in_flash = parse_config_from_flash();
	if (!config_in_flash) {
		spl("loading default config");
		// parse defaults because no config.json file or no extra config values besides brightness / wf creds
		default_json_config.replace("_api_username_",API_USERNAME);
		default_json_config.replace("_api_password_",API_PASSWORD);
	
		parse_json(default_json_config);
		json_to_config();
		
		// set some settings based on build flags
		#ifdef CELLULAR_RX
			m_config_map->put("aux_rf", "cellular");
		#elif defined(LORA_RX)
			m_config_map->put("aux_rf", "lora");
		#endif
		#ifdef NO_WIFI
			m_config_map->put("no_wifi", "true"); // we use this to disable wifi
		#endif

		// If this is first boot (boot_num = 0), try connecting with default credentials
		if (geti("boot_num") == 0) {
			spl("First boot - trying default WiFi credentials");
			WiFi.mode(WIFI_STA);
			WiFi.begin("BeeBee2.4", "bravestreet599");
			delay(5000); // Give it time to connect
		}

		// save out all config items to the json document
		config_to_json();
		// if we get here then we should write this back to the FS for next boot
		write_json_to_flash();
	} else {
		spl("Using config from flash");
	}
	json_to_config();  // copy config value to config's data_config_map object set boot_num 0

	// decide which client to use and set it to our member var
	if (m_config_map->has("aux_rf")) {
		if ((gets("aux_rf").equals("cellular") && geti("boot_num") % 2 == 0) || gets("no_wifi").equals("true")) {
			spl("Using cellular Client");
			m_wireless_mode = "cellular";
		} else {
			spl("Using WiFi Client");
			m_wireless_mode = "WiFi";  // default but set anyway.
			m_wireless_client = new WiFiClient();
			m_wireless_client_secure = new WiFiClientSecure();
		}
	} else {
		spl("Default to WiFi Client");
		m_wireless_mode = "WiFi";  // default but set anyway.
		m_wireless_client = new WiFiClient();
		m_wireless_client_secure = new WiFiClientSecure();
	}

// set night_mode 1,2,off
	// increment boot number
	m_config_map->put("boot_num", String(geti("boot_num") + 1));
	config_to_json();
	write_json_to_flash();
}

String Config::mph_string_to_rounded_kts_string(String mph) {
		float mph_int = mph.toFloat();
		float kts = mph_int * 0.868976;
		kts = kts +0.5;
		int kts_int = (int)kts;
		String kts_string = String(kts_int);
		return kts_string;
		
	}

// various logic based on data url and other config to determine which data_source object to use
const String Config::data_source() {
	// if we have mqtt in the data_url then return mqtt
	const String url = gets("api_data_url");

	if (gets("dtype").equals("twitchonair")) {
		return "twitchonair";
	}
	else if (gets("dtype").startsWith("wu_") || url.indexOf("api.weather.com") > -1 ) {
		return "wu";
	} 
	else if (gets("dtype").equals("word")) {
		return "word";
	}
	else if (url.indexOf("mqtt://") > -1) {
		return "mqtt";
	}
	else if (gets("dtype").startsWith("wf_") || url.indexOf("weatherflow") > -1 ) {
		return "wf";
	}
	else {
		spl2("NULL DATASOURCE for: [", url);
		return "null";
	}
}

int Config::night_mode_start() {
	return gets("night_mode").substring(0, 2).toInt();
}
int Config::night_mode_end() {
	return gets("night_mode").substring(3, 5).toInt();
}
String Config::night_mode_mode() {
	if (gets("night_mode").equals("none") || gets("night_mode").equals("null") || gets("night_mode").length() < 3) return "none";
	return gets("night_mode").substring(6, -1);
}
// bool Config::night_mode_active() {
// 	return night_mode_active(geti("hour"));
// }

bool Config::night_mode_active(int hour) {
	const String nightMode = gets("night_mode");
	spl2("night mode config: ", nightMode);
	spl2("night mode start is ", night_mode_start());
	spl2("night mode end is ", night_mode_end());
	spl2("hour is ", hour);
	spl2("night_mode_continues is ", night_mode_continues);
	if (nightMode.indexOf("none") > -1 || nightMode.length() < 9) {
		spl("night mode disabled (none or too short)");
		return false;
	}
	else if ((hour >= night_mode_start() && hour > 6) || (hour <= night_mode_end() && hour < 6)) {
		// night_mode_continues = true;  let the display classes set this
		spl("night mode IS active based on hour");
		return true;
	}
	else {
		spl("night mode NOT active based on hour");
		if (night_mode_continues) {
			night_mode_continues = false;
			spl("exiting night mode");
#ifndef LORA_RX
			// restart();   // restart when waking up - COMMENTED OUT TO PREVENT CRASH
#endif
		}
		return false;
	}
}

// build config url by compbining the tembplate with the mac_address
String Config::config_url() {
	String url = gets("config_url");
	String token = gets("access_token");
	if (token.equals("")) {
		token = m_mac_address;
	}
	url.replace("MAC_ADDRESS", m_mac_address);
	url.replace("ACCESS_TOKEN", token);

	// spl2("config url : ",url);
	return url;
}
// set force_wifi_config 1
// return the useragent.  something like Windy-Tron 1.0 / ESP8266,32 MAC adress
String Config::user_agent() {
	if (m_user_agent.isEmpty()) {
		m_user_agent = "General-Tron 2.0/";
		m_user_agent.concat(m_display_hw);
#ifdef FEATHERESP32
		m_user_agent.concat("-esp32-");
#else
		m_user_agent.concat("-esp8266-");
#endif

		m_user_agent.concat(m_mac_address);
	}
	return m_user_agent;
}

// iterate over config->m_config_map nad store them in a new json document
void Config::config_to_json() {
	spl("config_to_json");
	json_config.clear();

	for (int i = 0; i < m_config_map->size(); i++) {
		if (!m_config_map->getData(i).equals("")) {
			json_config[m_config_map->getKey(i)] = m_config_map->getData(i);
		}
	}
}

// shouldn't ever need to do anything here as all data is kept in json object and never transferred into member variables
void Config::json_to_config() {
	spl("json_to_config");

	JsonObject object = json_config.as<JsonObject>();

	for (auto kvp : object)
	{
		if (!kvp.value().isNull() && !(m_config_map->get(kvp.key().c_str()).equals(kvp.value().as<String>()))) {
			//spl2(kvp.key().c_str(),kvp.value().as<String>());
			m_config_map->put(kvp.key().c_str(), kvp.value());
		}
	}
}

int Config::http_post(String url, String post_data) {

	spl("in config::http post");
	int code = 500;
	String result_string;
	result_string.reserve(1024);
    String host;
    host = url;
	Client *client;
	int port = 80;
	if (host.indexOf("https://") > -1) {
		host.replace("https://", "");
		port = 443;
		static_cast<WiFiClientSecure*>(m_wireless_client_secure)->setInsecure();
		client = m_wireless_client_secure;
	} else {
		host.replace("http://", "");
		client = m_wireless_client;
	}
    host = host.substring(0,host.indexOf('/'));
	if (host.indexOf(":") > -1) {
		port = host.substring(host.indexOf(':')+1,host.indexOf('/')).toInt();
		host = host.substring(0,host.indexOf(':'));
	}
    spl("http host: "+host);
	spl2("port:" , port);
	//static_cast<WiFiClientSecure*>(m_wireless_client_secure)->setInsecure();
    if (!m_wireless_client_secure->connect(host.c_str(), port)) {
        spl("Connection failed : " + String(host));
        code = 400;
    } else {
        spl("Connected to host");
        // Make a HTTP request:
        m_wireless_client_secure->println("POST "+url+" HTTP/1.0");
        m_wireless_client_secure->println("Host: "+host);
        m_wireless_client_secure->println("User-Agent: General-Tron IoT");
        m_wireless_client_secure->println("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        m_wireless_client_secure->println("Accept-Language: en-US,en;q=0.9");
        m_wireless_client_secure->println("Content-Type: application/json");
        m_wireless_client_secure->println("Connection: close");
		m_wireless_client_secure->println("Content-Length: "+ String(post_data.length()));
    	m_wireless_client_secure->println();
    	m_wireless_client_secure->println(post_data);
    	m_wireless_client_secure->println();


        while (m_wireless_client_secure->connected() || m_wireless_client_secure->available()) {
            String line = m_wireless_client_secure->readStringUntil('\n');
			//spl(line);
            if (line.indexOf("HTTP") == 0) {
                spl(line);
                if (line.indexOf("200") > 0) {
					spl("setting code to 200");
					code = 200;
				}
				//if (line.indexOf("500" >= 0)) code = 500;
            }
            //spl(line);
            if (line.equals("\r")) {
                spl("end of headers");
                break;
            }
        }
        // if there are incoming bytes available
        // from the server, read them and print them:
        //String output = "";
		if (code == 200) {
			while (m_wireless_client_secure->connected() && m_wireless_client_secure->available()) {
				while (m_wireless_client_secure->available()) {
						char c = m_wireless_client_secure->read();
						result_string.concat(c);
					}
			}
			spl("out of body");

		} else {
			spl("code not 200, skipping body");
		}

        m_wireless_client_secure->stop();

        if (code == 200) {
			//return result_string;
			return 200;
        } else {
        	Serial.printf("got http code: %d",code);
        	return code;
        }		
	}
	return 500;
}

String Config::http_get(String url) {
	spl("in config::http get");
	int code;
	String result_string;
	result_string.reserve(1024);
    String host;
    host = url;
	int port = 80;
	Client *client;
	if (host.indexOf("https://") > -1) {
		host.replace("https://", "");
		port = 443;
		static_cast<WiFiClientSecure*>(m_wireless_client_secure)->setInsecure();
		client = m_wireless_client_secure;
	} else {
		host.replace("http://", "");
		client = m_wireless_client;
	}
    host = host.substring(0,host.indexOf('/'));

	// check for custom port
	if (host.indexOf(":") > -1) {
		port = host.substring(host.indexOf(':')+1,host.indexOf('/')).toInt();
		host = host.substring(0,host.indexOf(':'));
	}
    spl("host: "+host);
	spl2("port:" , port);
    if (!client->connect(host.c_str(), port)) {
        spl("Connection failed : " + String(host));
        code = 400;
    } else {
        spl("Connected to host");
        // Make a HTTP request:
        client->println("GET "+url+" HTTP/1.0");
        client->println("Host: "+host);
        client->println("User-Agent: General-Tron IoT");
        client->println("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        client->println("Accept-Language: en-US,en;q=0.9");
        client->println("Content-Type: application/x-www-form-urlencoded");
        client->println("Connection: close");
        client->println();

        while (client->connected() || client->available()) {
            String line = client->readStringUntil('\n');
            if (line.indexOf("HTTP") == 0) {
                spl(line);
                if (line.indexOf("200") > 0) code = 200;
				if (line.indexOf("404") > 0) return String(404);
            }
            //spl(line);
            if (line.equals("\r")) {
                spl("end of headers");
                break;
            }
        }
        // if there are incoming bytes available
        // from the server, read them and print them:
        //String output = "";
        while (client->connected() || client->available()) {
        	while (client->available()) {
                    char c = client->read();
                    result_string.concat(c);
                }
        }
		spl("out of body");

        client->stop();

        if (code == 200) {
			return result_string;
        } else {
        	Serial.printf("got http code: %d",code);
        	return String(code);
        }		
	}
	return String(500);
}

String Config::get_remote_config() {
	spl("in get remote config");
	String result_body = "";
	const String curl = config_url();
	spl(curl);
	if (curl.equals("")) {
		spl("CONFIG URL IS BLANK");
	} else {
		result_body = http_get(curl);
		if (result_body.length() < 5) {
			spl2("HTTP ERROR",result_body);
		} else if (result_body.length() > 600) {
			spl("remote config too large (> 600b)");
			result_body = "";
		}
		spl(result_body);
	}
	return result_body;
}

// must use SSL, skip if doing this over cellular TODO
int Config::post_telemetry(String s) {
	int code = 0;
	String t_url = config_url();
	t_url.replace("get_config", "post_telemetry"); //gtj style
	t_url.replace("attributes", "telemetry"); //thingsboard style

	spl2(t_url,s);
	code = http_post(t_url,s);
	spl2("telem_code: ", code);
	return code;
}


void Config::write_json_to_flash() {

	spl("writing json config to flash");
	File configFile = LittleFS.open("/config.json", "w");
	if (!configFile) {
		spl("failed to open config file for writing");
	}
	//serializeJsonPretty(json_config, Serial);

	serializeJson(json_config, configFile);
	configFile.close();

	//end save
}

bool Config::parse_config_from_flash() {
	// if json_config exists on filesystem

	bool config_in_flash = false;
  #ifdef ESP32
	if (LittleFS.begin(true)) {
  #else
	if (LittleFS.begin()) {
  #endif
		spl("mounted file system");
		if (LittleFS.exists("/config.json")) {

			File configFile = LittleFS.open("/config.json", "r");
			if (configFile) {
				spl("opened config file");
				size_t size = configFile.size();
				// Allocate a buffer to store contents of the file.
				std::unique_ptr<char[]> buf(new char[size]);

				configFile.readBytes(buf.get(), size);


				String json_string = String(buf.get());
				if (json_string.length() < 10) return false;  // probably corrupt if its so short
				spl(json_string);
				auto error = deserializeJson(json_config, json_string);
				if (error) {
					spl2(F("deserializeJson() failed with code "), error.c_str());
				}

				serializeJsonPretty(json_config, Serial);
				if (!error) {
					spl("\nparsed json_config");
					if (json_config.containsKey("config_url")) {
						spl("config_url key exists");
						config_in_flash = true;
					}
				}
				else {
					spl("failed to load json_config config");
				}
			}
		}
	}
	else {
		spl("failed to mount FS");
	}
	return config_in_flash;
}



bool Config::parse_json(String json) {
	spl(json);
	auto error = deserializeJson(json_config, json);
	if (error) {
		spl(F("deserializeJson() failed with code "));
		spl(String(error.c_str()));
		return false;
	}
	return true;
}


void Config::print() {
	serializeJsonPretty(json_config, Serial);
}


String Config::gets(const char name[]) {
	if (has(name)) {
		return m_config_map->get(name);
		//return json_config[name].as<String>();
	}
	else {
		//spl2("no exist: ",name);
		return "";
	}
}
int Config::geti(const char name[]) {
	if (has(name)) {

		return m_config_map->get(name).toInt();
		//		return json_config[name].as<int>();
	}
	else {
		spl2("no exist: ",name);
		return 0;
	}
}

int Config::api_server_port() {
	String data_url = gets("api_data_url");
	int port = 0;  // default is zero, if we don't change it then the caller should use a default port
	int dslash = data_url.indexOf("//");  // check for protocol eg http://
	if (dslash > 3) {
		data_url = data_url.substring(dslash + 2, -1); // strip it away
	}
	if (data_url.indexOf(":") > -1) {  // check for port
		// we have a defined port
		port = data_url.substring(data_url.indexOf(':') + 1, data_url.indexOf('/')).toInt();
	}
	return port;
}

String Config::api_server() {
	String data_url = gets("api_data_url");
	int dslash = data_url.indexOf("//");
	if (dslash > 3) {
		data_url = data_url.substring(dslash + 2, -1);
	}
	//data_url.substring(0,data_url.indexOf('/')).toCharArray(data_host,data_url.length());
	// need to strip out the port number if present
	int colon = data_url.indexOf(':');
	if (colon > -1) {
		// strip the port
		data_url = data_url.substring(0, colon);
	}
	else {
		data_url = data_url.substring(0, data_url.indexOf('/'));
	}
	return data_url;
}
String Config::api_data_path() {
	String data_url = gets("api_data_url");
	int dslash = data_url.indexOf("//");
	if (dslash > 3) {
		data_url = data_url.substring(dslash + 2, -1);
	}
	// return from first single slash to the end
	return data_url.substring(data_url.indexOf('/') + 1, -1);

}

bool Config::merge_remote_config(String str_json) {
	bool new_data = false;
	DynamicJsonDocument remote_json_config(600);

	auto error = deserializeJson(remote_json_config, str_json);
	if (error) {
		//spl(F("deserializeJson() failed with code "));
		spl(String(error.c_str()));
		return false;
	}

	JsonObject object;
	if (remote_json_config.containsKey("shared")) {
		spl("thingsboard config detected");
		object = remote_json_config["shared"].as<JsonObject>();

	}
	else {
		object = remote_json_config.as<JsonObject>();
	}
	//merge, 

	for (auto kvp : object)
	{
		if (!kvp.value().isNull() && !(json_config[kvp.key()].as<String>().equals(kvp.value().as<String>()))) {
			new_data = true;
			json_config[kvp.key()] = kvp.value();
			sp(kvp.key().c_str());spl2(" : ", json_config[kvp.key()].as<String>());
			if (json_config[kvp.key()].isNull()) {
				//delay(1000);
			}

		}
		else {

		}
	}
	//serializeJsonPretty(json_config,Serial);
	if (new_data) {
		//json_to_config();
		spl("new config values writing to json to filesystem");
		write_json_to_flash();
		json_to_config();
		return true;

	}
	else {
		spl("no new config data, skipping merge");
	}
	return false;

}

bool Config::has(String name) {
	if (m_config_map->has(name)) {
		String s = m_config_map->get(name);
		if (s.length() > 0 && !s.equals("null")) {
			return true;
		}
		else {
			return false;
		}
	}
	else {
		return false;
	}
}

unsigned long Config::update_interval_ms() {
	int min = geti("update_interval_minutes");
	if (min < 1) min = 1;
	return (unsigned long)min * 60 * 1000;
}



#ifndef A7
#define A7 A0
#endif


int Config::get_battery_mv() {
	int mv = 0;

#ifdef LILYGO
	digitalWrite(14, HIGH);
	delay(1);
	float measurement = (float)analogRead(35);
	float battery_voltage = (measurement / 4095.0) * 2.0 * 3.3 * (1100 / 1000.0);
	mv = int(battery_voltage*1000); //actually mv
	digitalWrite(14, LOW);
#else
	// multiply AD value by BAT_SCALE to get mV  
	delay(100);
	mv = int(analogRead(BAT_PIN) * BAT_SCALE); // m0 built in voltage divider
#endif
//int mv = 3660;
	delay(100);
	return mv;
}

String Config::get_battery_v() {
	return String(int(get_battery_mv() / 10) / 100.0);
}

// return 1-8 for battery level 4.2 is fully charged, 3.5 is emty
int Config::battery_level() {
	int mv = get_battery_mv();
	return constrain(map(mv, 4150, 3200, 8, 1), 1, 8);;
}

void Config::deep_sleep() {
	#ifdef ESP32
	uint64_t sleep_millis = geti("update_interval_minutes") * 60 * 1000;
    if (sleep_millis == 0) {

		esp_sleep_enable_timer_wakeup(43200000000L);
		spl("going into deepsleep for 12 hours");
	} else {
		esp_sleep_enable_timer_wakeup(sleep_millis*1000);
		spl2("going into deepsleep for usec: ", sleep_millis*1000);
	}
  
    esp_deep_sleep_start();
	#endif
}

void Config::restart() {
	#ifdef HUZZAH
	ESP.restart();
	#else
	WiFi.disconnect(true);
	esp_sleep_enable_timer_wakeup(0); // Set wake-up time to 10 seconds
    esp_deep_sleep_start();
	#endif 
}

bool Config::is_upsidedown() {
	if (gets("upsidedown").equals("true")) {
		return true;
	}
	else {
		return false;
	}
}


void Config::add_user_param(const char* name, const char* value) {
	m_config_map->put(name, value);

}
bool Config::delete_from_config(String key) {

	if (has(key)) {
		m_config_map->remove(key);
		config_to_json();
		write_json_to_flash();
		return true;
	}
	return false;
}

// check the d_opts config value for a string and number next to it
int Config::display_option(const char* name) {
	//spl2("opt: ", name);
	//spl2("d_opts: ", gets("d_opts"));
	int option_index = gets("d_opts").indexOf(name);
	//spl2("option index : ", option_index);
	// bva
	// check for presence of the option
	if (option_index > -1) { // && option_index+2 < (int)gets("d_opts").length()) {  // bug here if d_opts is only 2 chars for some reason

		// make sure we this is not the last char of the string
		if (option_index + 2 < (int)gets("d_opts").length()) {
			// try to parse an integer from the second char
			int param = gets("d_opts").substring(option_index + 1, option_index + 2).toInt();
			// if it's a sane value return it
			if (param > 0 && param < 10) return param;
		}
		// not sane value or single option just return 1
		return 1;
	}
	else {
		//spl2("no exist display option ", name);
		return -1;
	}

}
