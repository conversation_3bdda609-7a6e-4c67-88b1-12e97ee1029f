# This workflow was added by Code<PERSON>ee. Learn more at https://codesee.io/
# This is v2.0 of this workflow file
on:
  push:
    branches:
      - master
  pull_request_target:
    types: [opened, synchronize, reopened]

name: CodeSee

permissions: read-all

jobs:
  codesee:
    runs-on: ubuntu-latest
    continue-on-error: true
    name: Analyze the repo with CodeSee
    steps:
      - uses: Codesee-io/codesee-action@v2
        with:
          codesee-token: ${{ secrets.CODESEE_ARCH_DIAG_API_TOKEN }}
          codesee-url: https://app.codesee.io
